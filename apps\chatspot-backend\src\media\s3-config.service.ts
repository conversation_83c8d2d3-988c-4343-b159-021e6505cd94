import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { S3Client } from '@aws-sdk/client-s3';

@Injectable()
export class S3ConfigService {
  private s3Client: S3Client;
  private bucketName: string;
  private useS3: boolean;

  constructor(private configService: ConfigService) {
    this.useS3 = this.configService.get('USE_S3_STORAGE') === 'true';
    this.bucketName = this.configService.get('AWS_S3_BUCKET') || '';

    if (this.useS3) {
      const accessKeyId = this.configService.get('AWS_ACCESS_KEY_ID');
      const secretAccessKey = this.configService.get('AWS_SECRET_ACCESS_KEY');

      if (!accessKeyId || !secretAccessKey || !this.bucketName) {
        throw new Error('AWS credentials and bucket name are required when USE_S3_STORAGE is true');
      }

      this.s3Client = new S3Client({
        region: this.configService.get('AWS_REGION') || 'us-east-1',
        credentials: {
          accessKeyId,
          secretAccessKey,
        },
      });
    }
  }

  getS3Client(): S3Client {
    return this.s3Client;
  }

  getBucketName(): string {
    return this.bucketName;
  }

  isS3Enabled(): boolean {
    return this.useS3;
  }

  getS3Url(key: string): string | null {
    if (!this.useS3) {
      return null;
    }
    const region = this.configService.get('AWS_REGION') || 'us-east-1';
    return `https://${this.bucketName}.s3.${region}.amazonaws.com/${key}`;
  }
}
