import {
  <PERSON>,
  Post,
  Get,
  Delete,
  Param,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
  Res,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { MediaService } from './media.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Media } from './media.entity';

@ApiTags('media')
@Controller('api/media')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class MediaController {
  constructor(private readonly mediaService: MediaService) {}

  @Post('upload')
  @ApiOperation({ summary: 'Upload a media file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Media file to upload',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Media uploaded successfully',
    type: Media,
  })
  @ApiResponse({ status: 400, description: 'Invalid file or file too large' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadMedia(
    @UploadedFile() file: Express.Multer.File,
    @Request() req: any,
  ): Promise<Media> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    const username = req.user.username;
    return this.mediaService.uploadMedia(file, username);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get media file' })
  @ApiResponse({
    status: 200,
    description: 'Media file retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Media not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getMediaFile(
    @Param('id') id: string,
    @Res() res: Response,
  ): Promise<void> {
    const { media, buffer } = await this.mediaService.getMediaFile(id);
    
    res.set({
      'Content-Type': media.mime_type,
      'Content-Length': media.file_size.toString(),
      'Content-Disposition': `inline; filename="${media.original_filename}"`,
      'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
    });
    
    res.send(buffer);
  }

  @Get(':id/thumbnail')
  @ApiOperation({ summary: 'Get media thumbnail' })
  @ApiResponse({
    status: 200,
    description: 'Thumbnail retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Thumbnail not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getThumbnail(
    @Param('id') id: string,
    @Res() res: Response,
  ): Promise<void> {
    const { media, buffer } = await this.mediaService.getThumbnail(id);
    
    res.set({
      'Content-Type': 'image/jpeg',
      'Content-Length': buffer.length.toString(),
      'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
    });
    
    res.send(buffer);
  }

  @Get(':id/info')
  @ApiOperation({ summary: 'Get media information' })
  @ApiResponse({
    status: 200,
    description: 'Media information retrieved successfully',
    type: Media,
  })
  @ApiResponse({ status: 404, description: 'Media not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getMediaInfo(@Param('id') id: string): Promise<Media> {
    return this.mediaService.getMedia(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a media file' })
  @ApiResponse({
    status: 200,
    description: 'Media deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Media not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async deleteMedia(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<{ message: string }> {
    // TODO: Add authorization check - only allow deletion by uploader or admin
    await this.mediaService.deleteMedia(id);
    return { message: 'Media deleted successfully' };
  }

  @Get('user/my-media')
  @ApiOperation({ summary: 'Get all media uploaded by the current user' })
  @ApiResponse({
    status: 200,
    description: 'User media retrieved successfully',
    type: [Media],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getMyMedia(@Request() req: any): Promise<Media[]> {
    const username = req.user.username;
    return this.mediaService.getMediaByUser(username);
  }
}
