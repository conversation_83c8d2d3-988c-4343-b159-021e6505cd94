{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-c369e3818a2a22c8bc35.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "rndocumentpickerCGen_autolinked_build", "jsonFile": "directory-rndocumentpickerCGen_autolinked_build-Debug-7bc18fc4c52c4532991c.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/@react-native-documents/picker/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "RNBootSplashSpec_autolinked_build", "jsonFile": "directory-RNBootSplashSpec_autolinked_build-Debug-5386160120e70abe6baf.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-54481c10ece14403fea8.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [7]}, {"build": "RNImagePickerSpec_autolinked_build", "jsonFile": "directory-RNImagePickerSpec_autolinked_build-Debug-a3fd37fa8efb8eef71f5.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "RNPermissionsSpec_autolinked_build", "jsonFile": "directory-RNPermissionsSpec_autolinked_build-Debug-9bc20f34c46a7cf8bd44.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-permissions/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-6d3e4fe85881b36934f3.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [10]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-84ae9d9c119ca1c187a6.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [8]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-Debug-ea2e83a74a0c848f9d19.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [9]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-09f56ec1595d2d2f23c3.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [4]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-7429e315647958b880a0.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_RNBootSplashSpec::@05d5bd8b08339ce1ebaa", "jsonFile": "target-react_codegen_RNBootSplashSpec-Debug-c25015e18bd62a9addf0.json", "name": "react_codegen_RNBootSplashSpec", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4", "jsonFile": "target-react_codegen_RNImagePickerSpec-Debug-c10d55dcba6c11e46d77.json", "name": "react_codegen_RNImagePickerSpec", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_RNPermissionsSpec::@7ad697819b753921c957", "jsonFile": "target-react_codegen_RNPermissionsSpec-Debug-6e5411484eef985dcdea.json", "name": "react_codegen_RNPermissionsSpec", "projectIndex": 0}, {"directoryIndex": 10, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-ce49b9d674baf9d37b45.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-f36f04c9f3120c2161e6.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rndocumentpickerCGen::@aac1846a245ca418eb66", "jsonFile": "target-react_codegen_rndocumentpickerCGen-Debug-db21c010ec7ea26ef90a.json", "name": "react_codegen_rndocumentpickerCGen", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-4e98fbef5a5125ffa05d.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-c413ea8926236267242f.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 9, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-Debug-2766bed3d86d4e7ddff6.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-d8a844006d8792794ffe.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/projecttest/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/6n2k2b6u/x86_64", "source": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}