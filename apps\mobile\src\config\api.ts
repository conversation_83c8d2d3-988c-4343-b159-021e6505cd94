import { getEnvVar } from '../utils/env';

// Get API URL from environment or use default
export const API_BASE_URL = getEnvVar('REACT_APP_API_URL') || 'http://localhost:3002';

// API endpoints
export const API_ENDPOINTS = {
  // Auth endpoints
  LOGIN: '/api/auth/login',
  REGISTER: '/api/auth/register',
  REFRESH: '/api/auth/refresh',
  
  // Media endpoints
  MEDIA_UPLOAD: '/api/media/upload',
  MEDIA_GET: (id: string) => `/api/media/${id}`,
  MEDIA_THUMBNAIL: (id: string) => `/api/media/${id}/thumbnail`,
  MEDIA_INFO: (id: string) => `/api/media/${id}/info`,
  MEDIA_DELETE: (id: string) => `/api/media/${id}`,
  MEDIA_USER: '/api/media/user/my-media',
};

// Helper function to build full URL
export const buildApiUrl = (endpoint: string): string => {
  return `${API_BASE_URL}${endpoint}`;
};

// Helper function to get auth headers
export const getAuthHeaders = (token?: string) => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
};

// Helper function to get multipart headers for file upload
export const getMultipartHeaders = (token?: string) => {
  const headers: Record<string, string> = {
    'Content-Type': 'multipart/form-data',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
};
